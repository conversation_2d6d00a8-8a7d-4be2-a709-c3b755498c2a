#!/usr/bin/env ts-node

import * as fs from 'fs/promises';
import * as path from 'path';
import { spawn } from 'child_process';

/**
 * Comprehensive build verification script
 */
class BuildVerification {
  private projectRoot = process.cwd();
  private errors: string[] = [];
  private warnings: string[] = [];
  private buildLog: string[] = [];

  async run(): Promise<void> {
    console.log('🔍 Starting comprehensive build verification...\n');

    try {
      // 1. Pre-build checks
      await this.preBuildChecks();
      
      // 2. TypeScript compilation
      await this.verifyTypeScriptCompilation();
      
      // 3. Module exports verification
      await this.verifyModuleExports();
      
      // 4. Tool registration verification
      await this.verifyToolRegistration();
      
      // 5. Dependency verification
      await this.verifyDependencies();
      
      // 6. Runtime verification
      await this.verifyRuntimeStartup();
      
      // 7. Generate verification report
      await this.generateVerificationReport();
      
      if (this.errors.length === 0) {
        console.log('\n✅ Build verification completed successfully!');
        console.log('📋 Check build-verification-report.md for detailed information');
      } else {
        console.log('\n❌ Build verification failed with errors:');
        this.errors.forEach(error => console.log(`  - ${error}`));
        process.exit(1);
      }
      
    } catch (error) {
      console.error('❌ Build verification failed:', error);
      process.exit(1);
    }
  }

  private async preBuildChecks(): Promise<void> {
    console.log('🔍 Running pre-build checks...');
    
    // Check required files exist
    const requiredFiles = [
      'package.json',
      'tsconfig.json',
      'src/index.ts',
      'src/server.ts'
    ];
    
    for (const file of requiredFiles) {
      const filePath = path.join(this.projectRoot, file);
      try {
        await fs.access(filePath);
        console.log(`  ✓ ${file} exists`);
      } catch (error) {
        this.errors.push(`Required file missing: ${file}`);
        console.log(`  ❌ ${file} missing`);
      }
    }
    
    // Check TypeScript configuration
    await this.checkTypeScriptConfig();
    
    // Check package.json structure
    await this.checkPackageJson();
    
    this.buildLog.push('Pre-build checks completed');
  }

  private async checkTypeScriptConfig(): Promise<void> {
    try {
      const tsconfigPath = path.join(this.projectRoot, 'tsconfig.json');
      const tsconfigContent = await fs.readFile(tsconfigPath, 'utf-8');
      const tsconfig = JSON.parse(tsconfigContent);
      
      // Check essential compiler options
      const requiredOptions = {
        'target': 'ES2022',
        'module': 'Node16',
        'moduleResolution': 'Node16',
        'strict': true
      };
      
      for (const [option, expectedValue] of Object.entries(requiredOptions)) {
        if (tsconfig.compilerOptions[option] !== expectedValue) {
          this.warnings.push(`TypeScript ${option} should be ${expectedValue}, found ${tsconfig.compilerOptions[option]}`);
        }
      }
      
      console.log('  ✓ TypeScript configuration validated');
    } catch (error) {
      this.errors.push(`Failed to validate TypeScript configuration: ${error}`);
    }
  }

  private async checkPackageJson(): Promise<void> {
    try {
      const packagePath = path.join(this.projectRoot, 'package.json');
      const packageContent = await fs.readFile(packagePath, 'utf-8');
      const packageJson = JSON.parse(packageContent);
      
      // Check essential fields
      const requiredFields = ['name', 'version', 'type', 'bin', 'scripts'];
      for (const field of requiredFields) {
        if (!packageJson[field]) {
          this.errors.push(`package.json missing required field: ${field}`);
        }
      }
      
      // Check if type is module
      if (packageJson.type !== 'module') {
        this.errors.push('package.json type should be "module"');
      }
      
      // Check essential scripts
      const requiredScripts = ['build', 'clean', 'start'];
      for (const script of requiredScripts) {
        if (!packageJson.scripts[script]) {
          this.warnings.push(`package.json missing recommended script: ${script}`);
        }
      }
      
      console.log('  ✓ package.json structure validated');
    } catch (error) {
      this.errors.push(`Failed to validate package.json: ${error}`);
    }
  }

  private async verifyTypeScriptCompilation(): Promise<void> {
    console.log('🔨 Verifying TypeScript compilation...');
    
    return new Promise((resolve, reject) => {
      const tsc = spawn('npx', ['tsc', '--noEmit'], {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      
      let stdout = '';
      let stderr = '';
      
      tsc.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      tsc.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      tsc.on('close', (code) => {
        if (code === 0) {
          console.log('  ✓ TypeScript compilation successful');
          this.buildLog.push('TypeScript compilation passed');
          resolve();
        } else {
          console.log('  ❌ TypeScript compilation failed');
          this.errors.push(`TypeScript compilation failed: ${stderr}`);
          this.buildLog.push(`TypeScript compilation failed: ${stderr}`);
          resolve(); // Continue with other checks
        }
      });
      
      tsc.on('error', (error) => {
        this.errors.push(`Failed to run TypeScript compiler: ${error.message}`);
        resolve();
      });
    });
  }

  private async verifyModuleExports(): Promise<void> {
    console.log('📦 Verifying module exports...');
    
    // Check all index.ts files have proper exports
    const indexFiles = await this.findIndexFiles();
    
    for (const indexFile of indexFiles) {
      await this.checkIndexFileExports(indexFile);
    }
    
    this.buildLog.push('Module exports verification completed');
  }

  private async findIndexFiles(): Promise<string[]> {
    const indexFiles: string[] = [];
    
    async function searchDirectory(dir: string): Promise<void> {
      try {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          
          if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules' && entry.name !== 'dist') {
            await searchDirectory(fullPath);
          } else if (entry.isFile() && entry.name === 'index.ts') {
            indexFiles.push(fullPath);
          }
        }
      } catch (error) {
        // Ignore directories we can't read
      }
    }
    
    await searchDirectory(path.join(this.projectRoot, 'src'));
    return indexFiles;
  }

  private async checkIndexFileExports(indexFile: string): Promise<void> {
    try {
      const content = await fs.readFile(indexFile, 'utf-8');
      const relativePath = path.relative(this.projectRoot, indexFile);
      
      // Check if file has exports
      if (!content.includes('export')) {
        this.warnings.push(`${relativePath} has no exports`);
        console.log(`  ⚠️  ${relativePath} has no exports`);
      } else {
        console.log(`  ✓ ${relativePath} has exports`);
      }
      
      // Check for common export patterns
      if (content.includes('export function register') || content.includes('export {')) {
        console.log(`  ✓ ${relativePath} has proper export patterns`);
      }
      
    } catch (error) {
      this.warnings.push(`Failed to check exports in ${indexFile}: ${error}`);
    }
  }

  private async verifyToolRegistration(): Promise<void> {
    console.log('🛠️  Verifying tool registration...');
    
    // This would ideally import and check the actual tool registration
    // For now, we'll do a basic check of the server.ts file
    try {
      const serverPath = path.join(this.projectRoot, 'src/server.ts');
      const serverContent = await fs.readFile(serverPath, 'utf-8');
      
      // Check for tool registration calls
      const registrationPatterns = [
        'registerProjectTools',
        'registerFileTools',
        'registerBuildTools',
        'registerEnterpriseFileTools',
        'registerEnterpriseXcodeTools',
        'registerEnterpriseBuildTools'
      ];
      
      for (const pattern of registrationPatterns) {
        if (serverContent.includes(pattern)) {
          console.log(`  ✓ ${pattern} found`);
        } else {
          this.warnings.push(`Tool registration function ${pattern} not found in server.ts`);
        }
      }
      
      this.buildLog.push('Tool registration verification completed');
    } catch (error) {
      this.errors.push(`Failed to verify tool registration: ${error}`);
    }
  }

  private async verifyDependencies(): Promise<void> {
    console.log('📚 Verifying dependencies...');
    
    return new Promise((resolve) => {
      const npm = spawn('npm', ['audit', '--audit-level=moderate'], {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      
      let stdout = '';
      let stderr = '';
      
      npm.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      npm.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      npm.on('close', (code) => {
        if (code === 0) {
          console.log('  ✓ No security vulnerabilities found');
        } else {
          this.warnings.push(`npm audit found issues: ${stdout}`);
          console.log('  ⚠️  npm audit found potential issues');
        }
        
        this.buildLog.push('Dependency verification completed');
        resolve();
      });
      
      npm.on('error', (error) => {
        this.warnings.push(`Failed to run npm audit: ${error.message}`);
        resolve();
      });
    });
  }

  private async verifyRuntimeStartup(): Promise<void> {
    console.log('🚀 Verifying runtime startup...');
    
    // First ensure the project is built
    await this.buildProject();
    
    // Then test if the server can start
    return new Promise((resolve) => {
      const server = spawn('timeout', ['5s', 'node', 'dist/index.js'], {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      
      let stdout = '';
      let stderr = '';
      
      server.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      server.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      server.on('close', (code) => {
        // Code 124 means timeout (expected for a server)
        // Code 0 means clean exit
        if (code === 124 || code === 0) {
          console.log('  ✓ Server starts successfully');
          this.buildLog.push('Runtime startup verification passed');
        } else {
          console.log('  ❌ Server failed to start');
          this.errors.push(`Server startup failed with code ${code}: ${stderr}`);
          this.buildLog.push(`Server startup failed: ${stderr}`);
        }
        resolve();
      });
      
      server.on('error', (error) => {
        this.errors.push(`Failed to test server startup: ${error.message}`);
        resolve();
      });
    });
  }

  private async buildProject(): Promise<void> {
    return new Promise((resolve) => {
      const build = spawn('npm', ['run', 'build'], {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      
      build.on('close', (code) => {
        if (code === 0) {
          console.log('  ✓ Project built successfully');
        } else {
          this.errors.push('Failed to build project');
        }
        resolve();
      });
      
      build.on('error', (error) => {
        this.errors.push(`Build process failed: ${error.message}`);
        resolve();
      });
    });
  }

  private async generateVerificationReport(): Promise<void> {
    const reportPath = path.join(this.projectRoot, 'build-verification-report.md');
    
    const report = `# Build Verification Report

Generated: ${new Date().toISOString()}

## Summary

- **Errors**: ${this.errors.length}
- **Warnings**: ${this.warnings.length}
- **Status**: ${this.errors.length === 0 ? '✅ PASSED' : '❌ FAILED'}

## Errors

${this.errors.length === 0 ? 'No errors found.' : this.errors.map(error => `- ${error}`).join('\n')}

## Warnings

${this.warnings.length === 0 ? 'No warnings found.' : this.warnings.map(warning => `- ${warning}`).join('\n')}

## Build Log

${this.buildLog.map(entry => `- ${entry}`).join('\n')}

## Verification Steps

1. ✅ Pre-build checks (required files, configuration)
2. ✅ TypeScript compilation verification
3. ✅ Module exports verification
4. ✅ Tool registration verification
5. ✅ Dependency security audit
6. ✅ Runtime startup verification

## Next Steps

${this.errors.length === 0 
  ? '🎉 All verification steps passed! The project is ready for deployment.'
  : '🔧 Please fix the errors listed above before proceeding with deployment.'
}

${this.warnings.length > 0 
  ? '\n⚠️  Consider addressing the warnings to improve code quality.'
  : ''
}
`;

    await fs.writeFile(reportPath, report);
    console.log('  ✓ Generated build verification report');
  }
}

// Run verification if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const verification = new BuildVerification();
  verification.run().catch(console.error);
}
