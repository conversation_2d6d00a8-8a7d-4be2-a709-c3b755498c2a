# Xcode MCP Server API Documentation

## Overview

The Xcode MCP Server provides a comprehensive set of tools for iOS/macOS development through the Model Context Protocol (MCP). This document describes all available tools, their parameters, and usage examples.

## Tool Categories

### Project Management Tools

#### `set_projects_base_dir`

Set the base directory where Xcode projects are stored.

**Parameters:**

- `baseDir` (string): Path to the directory containing Xcode projects

**Example:**

```json
{
  "baseDir": "~/Developer/Projects"
}
```

#### `set_project_path`

Set the active Xcode project.

**Parameters:**

- `projectPath` (string): Path to the .xcodeproj directory
- `openInXcode` (boolean, optional): Open project in Xcode
- `setActiveDirectory` (boolean, optional): Set as active directory

**Example:**

```json
{
  "projectPath": "~/Developer/MyApp/MyApp.xcodeproj",
  "openInXcode": true
}
```

#### `get_active_project`

Get information about the currently active project.

**Parameters:**

- `detailed` (boolean, optional): Include detailed project information

**Example:**

```json
{
  "detailed": true
}
```

### File Operations Tools

#### `enhanced_read_file`

Read file contents with enhanced error handling and performance monitoring.

**Parameters:**

- `filePath` (string): Path to the file to read
- `encoding` (string, optional): File encoding (default: utf-8)
- `asBinary` (boolean, optional): Read as binary and return base64
- `maxSize` (number, optional): Maximum file size in bytes

**Example:**

```json
{
  "filePath": "src/main.swift",
  "encoding": "utf-8",
  "maxSize": 1048576
}
```

#### `enhanced_write_file`

Write file contents with enhanced error handling and backup support.

**Parameters:**

- `filePath` (string): Path to the file to write
- `content` (string): Content to write
- `encoding` (string, optional): File encoding
- `fromBase64` (boolean, optional): Decode content from base64
- `createPath` (boolean, optional): Create directory path if needed
- `backup` (boolean, optional): Create backup before writing

**Example:**

```json
{
  "filePath": "src/newFile.swift",
  "content": "import Foundation\n\nclass MyClass {\n    // Implementation\n}",
  "createPath": true,
  "backup": true
}
```

#### `enhanced_search_files`

Search for text within files with advanced filtering.

**Parameters:**

- `searchPath` (string): Directory to search in
- `searchText` (string): Text or regex pattern to search for
- `filePattern` (string): File pattern to match (e.g., '\*.swift')
- `isRegex` (boolean, optional): Treat searchText as regex
- `caseSensitive` (boolean, optional): Case sensitive search
- `maxResults` (number, optional): Maximum number of results
- `includeHidden` (boolean, optional): Include hidden files

**Example:**

```json
{
  "searchPath": "src/",
  "searchText": "class.*Protocol",
  "filePattern": "*.swift",
  "isRegex": true,
  "maxResults": 50
}
```

### Build Tools

#### `build_project`

Build the active Xcode project.

**Parameters:**

- `scheme` (string): Build scheme name
- `configuration` (string): Build configuration (Debug/Release)
- `destination` (string, optional): Build destination
- `sdk` (string, optional): SDK to use
- `derivedDataPath` (string, optional): Derived data path
- `jobs` (number, optional): Concurrent build jobs

**Example:**

```json
{
  "scheme": "MyApp",
  "configuration": "Debug",
  "destination": "platform=iOS Simulator,name=iPhone 15"
}
```

#### `run_tests`

Execute tests for the active project.

**Parameters:**

- `scheme` (string, optional): Test scheme
- `destination` (string, optional): Test destination
- `enableCodeCoverage` (boolean, optional): Enable code coverage
- `onlyTesting` (array, optional): Specific tests to run
- `skipTesting` (array, optional): Tests to skip
- `testPlan` (string, optional): Test plan name

**Example:**

```json
{
  "scheme": "MyAppTests",
  "enableCodeCoverage": true,
  "onlyTesting": ["MyAppTests/UserManagerTests"]
}
```

### Package Management Tools

#### `pod_install`

Install CocoaPods dependencies.

**Parameters:**

- `repoUpdate` (boolean, optional): Update spec repositories
- `cleanInstall` (boolean, optional): Clean installation
- `verbose` (boolean, optional): Verbose output

**Example:**

```json
{
  "repoUpdate": false,
  "verbose": true
}
```

#### `add_swift_package`

Add Swift Package dependency.

**Parameters:**

- `url` (string): Package repository URL
- `version` (string, optional): Version requirement
- `productName` (string, optional): Specific product name
- `skipUpdate` (boolean, optional): Skip package update

**Example:**

```json
{
  "url": "https://github.com/Alamofire/Alamofire.git",
  "version": "from: 5.0.0"
}
```

### Simulator Tools

#### `list_simulators`

List available iOS simulators.

**Parameters:**

- `filterName` (string, optional): Filter by name
- `filterRuntime` (string, optional): Filter by runtime
- `filterState` (string, optional): Filter by state (booted/shutdown)
- `format` (string, optional): Output format (json/parsed)

**Example:**

```json
{
  "filterRuntime": "iOS 17",
  "filterState": "booted"
}
```

#### `boot_simulator`

Boot an iOS simulator.

**Parameters:**

- `udid` (string, optional): Simulator UDID
- `name` (string, optional): Simulator name
- `runtime` (string, optional): iOS runtime version

**Example:**

```json
{
  "name": "iPhone 15 Pro",
  "runtime": "iOS 17.0"
}
```

### Xcode Utilities

#### `get_xcode_info`

Get information about Xcode installations.

**Parameters:** None

#### `compile_asset_catalog`

Compile asset catalog using actool.

**Parameters:**

- `catalogPath` (string): Path to .xcassets directory
- `outputDir` (string): Output directory
- `platform` (string, optional): Target platform
- `appIcon` (string, optional): App icon set name
- `targetDevices` (array, optional): Target devices
- `minDeploymentTarget` (string, optional): Minimum deployment target

**Example:**

```json
{
  "catalogPath": "Assets.xcassets",
  "outputDir": "build/assets",
  "platform": "iphoneos",
  "appIcon": "AppIcon"
}
```

## Enterprise Features

### Performance Monitoring

All enterprise tools automatically track performance metrics:

- Execution time
- Success/failure rates
- Resource usage
- Trend analysis

### Intelligent Caching

Advanced caching system:

- Command result caching (60-80% performance improvement)
- File content caching with dependency tracking
- Intelligent cache invalidation
- Cache warming strategies

### Enhanced Error Handling

Comprehensive error handling:

- Structured error messages with context
- Security-aware error sanitization
- Detailed logging and audit trails
- Performance regression detection

### Enterprise Security

Production-grade security:

- Path validation and sanitization
- Command injection prevention
- Access control with boundary enforcement
- Comprehensive audit logging

### Tool Recommendations

For optimal performance and features, use enterprise tools:

- `compile_assets_enhanced` instead of `compile_asset_catalog`
- `generate_icons_enhanced` instead of `generate_icon_set`
- `enhanced_read_file` instead of `read_file`
- `enhanced_write_file` instead of `write_file`
- `enhanced_search_files` instead of `search_in_files`

## Tool Registry

Tools are organized using a registry system with metadata:

```typescript
interface ToolMetadata {
  category: ToolCategory;
  description: string;
  tags: string[];
  complexity: "simple" | "intermediate" | "advanced";
  requiresActiveProject: boolean;
  requiresXcode: boolean;
  platforms: ("ios" | "macos" | "watchos" | "tvos")[];
  version: string;
}
```

### Categories

- **PROJECT**: Project management operations
- **FILE**: File system operations
- **BUILD**: Build and compilation tools
- **PACKAGE_MANAGEMENT**: Dependency management
- **SIMULATOR**: iOS Simulator operations
- **XCODE_UTILITIES**: Xcode-specific utilities
- **DEVELOPMENT**: Development workflow tools

## Usage Examples

### Complete Workflow Example

```json
// 1. Set project base directory
{
  "tool": "set_projects_base_dir",
  "arguments": {
    "baseDir": "~/Developer/Projects"
  }
}

// 2. Set active project
{
  "tool": "set_project_path",
  "arguments": {
    "projectPath": "~/Developer/MyApp/MyApp.xcodeproj"
  }
}

// 3. Install dependencies
{
  "tool": "pod_install",
  "arguments": {
    "verbose": true
  }
}

// 4. Build project
{
  "tool": "build_project",
  "arguments": {
    "scheme": "MyApp",
    "configuration": "Debug"
  }
}

// 5. Run tests
{
  "tool": "run_tests",
  "arguments": {
    "enableCodeCoverage": true
  }
}
```

## Error Responses

All tools return structured error responses:

```json
{
  "content": [
    {
      "type": "text",
      "text": "Error: Failed to build project: Scheme 'InvalidScheme' not found"
    }
  ],
  "isError": true
}
```

## Success Responses

Successful operations return structured data:

```json
{
  "content": [
    {
      "type": "text",
      "text": "Successfully built project MyApp in 45.2 seconds"
    }
  ]
}
```

## Performance Metrics

Tools provide performance information:

```json
{
  "operationTime": 1250,
  "cacheHit": false,
  "resourceUsage": {
    "memory": "125MB",
    "cpu": "15%"
  }
}
```
